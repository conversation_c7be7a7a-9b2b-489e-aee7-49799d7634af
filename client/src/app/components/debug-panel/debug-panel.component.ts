import { Component, inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { AdvertisingService } from '../../services/advertising.service';
import { environment } from '@/env/environment';

@Component({
  selector: 'app-debug-panel',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="debug-panel" style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 5px; font-size: 12px; z-index: 9999;">
      <h4>Debug Panel</h4>
      <p>Environment: {{ environment.apiUrl }}</p>
      <p>Platform: {{ isPlatformBrowser(platformId) ? 'Browser' : 'Server' }}</p>
      <p>Ads Status: {{ adsStatus }}</p>
      <button (click)="testApiConnection()" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">
        Test API
      </button>
      @if (apiTestResult) {
        <div style="margin-top: 10px; padding: 5px; background: rgba(255,255,255,0.1); border-radius: 3px;">
          <strong>API Test Result:</strong>
          <pre style="font-size: 10px; margin: 5px 0;">{{ apiTestResult }}</pre>
        </div>
      }
    </div>
  `
})
export class DebugPanelComponent {
  private platformId = inject(PLATFORM_ID);
  private advertisingService = inject(AdvertisingService);
  
  environment = environment;
  adsStatus = 'Not tested';
  apiTestResult = '';
  
  isPlatformBrowser = isPlatformBrowser;

  testApiConnection(): void {
    if (!isPlatformBrowser(this.platformId)) {
      this.apiTestResult = 'Cannot test API from server side';
      return;
    }

    this.adsStatus = 'Testing...';
    this.advertisingService.getAll().subscribe({
      next: (res: any) => {
        this.adsStatus = `Success: ${res.length} ads found`;
        this.apiTestResult = JSON.stringify(res, null, 2);
      },
      error: (err) => {
        this.adsStatus = `Error: ${err.status} ${err.statusText}`;
        this.apiTestResult = JSON.stringify({
          status: err.status,
          statusText: err.statusText,
          url: err.url,
          message: err.message,
          error: err.error
        }, null, 2);
      }
    });
  }
}
