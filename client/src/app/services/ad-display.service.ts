import { Injectable, inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Subject, takeUntil } from 'rxjs';
import { AdvertisingService } from './advertising.service';

export interface Advertisement {
  id: number;
  title: string;
  description: string;
  link: string;
  freq: number;
  active: boolean;
  image?: {
    name: string;
    path: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class AdDisplayService {
  private platformId = inject(PLATFORM_ID);
  private advertisingService = inject(AdvertisingService);
  
  private advertisements: Advertisement[] = [];
  private adShownCounts: Map<number, number> = new Map();
  private adTimerId: any = null;
  private adIntervalId: any = null;
  private destroy$ = new Subject<void>();
  
  // Observable for components to listen to ad display events
  private showAdSubject = new BehaviorSubject<Advertisement | null>(null);
  public showAd$ = this.showAdSubject.asObservable();

  // Subject for modal close events
  private modalClosedSubject = new Subject<void>();
  public modalClosed$ = this.modalClosedSubject.asObservable();

  private isInitialized = false;
  private isWaitingForModalClose = false;

  constructor() {
    // Auto-initialize when service is created
    this.initialize();

    // Listen for modal close events to schedule next ad
    this.modalClosed$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        console.log('AdDisplayService: Modal closed event received');
        if (this.isWaitingForModalClose) {
          console.log('AdDisplayService: Scheduling next ad after modal close');
          this.isWaitingForModalClose = false;
          this.scheduleNextAd();
        }
      });
  }

  /**
   * Initialize the ad display system
   */
  private initialize(): void {
    if (!isPlatformBrowser(this.platformId) || this.isInitialized) {
      return;
    }

    this.isInitialized = true;
    this.loadAdvertisements();
  }

  /**
   * Load advertisements from the API
   */
  private loadAdvertisements(): void {
    console.log('AdDisplayService: Loading advertisements from API...');
    this.advertisingService.getAll()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          console.log('AdDisplayService: Successfully loaded advertisements:', res);
          this.advertisements = res.filter((ad: any) => ad.active === true);
          console.log('AdDisplayService: Active advertisements:', this.advertisements.length);

          if (this.advertisements.length === 0) {
            console.warn('AdDisplayService: No active advertisements found');
            return;
          }

          // Initialize shown counts for each advertisement
          this.advertisements.forEach(ad => {
            this.adShownCounts.set(ad.id, 0);
          });

          // Start ad display after loading
          this.startAdDisplay();
        },
        error: (err) => {
          console.error('AdDisplayService: Failed to load advertisements:', err);
          console.error('AdDisplayService: Error details:', {
            status: err.status,
            statusText: err.statusText,
            url: err.url,
            message: err.message
          });
        }
      });
  }

  /**
   * Start the ad display system with initial delay and intervals
   */
  private startAdDisplay(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    // Clear any existing timers
    this.clearAdTimers();

    // Show first ad after 20 seconds
    this.adTimerId = setTimeout(() => {
      this.showNextAd();
    }, 20000); // 20 seconds
  }

  /**
   * Schedule the next ad after modal is closed
   */
  private scheduleNextAd(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    // Clear any existing timer
    if (this.adTimerId) {
      clearTimeout(this.adTimerId);
      this.adTimerId = null;
    }

    console.log('AdDisplayService: Scheduling next ad in 30 seconds');
    // Schedule next ad in 30 seconds
    this.adTimerId = setTimeout(() => {
      this.showNextAd();
    }, 30000); // 30 seconds
  }

  /**
   * Show the next available advertisement
   */
  private showNextAd(): void {
    if (!this.advertisements || this.advertisements.length === 0) {
      return;
    }
    
    // Find ads that haven't reached their frequency limit
    const availableAds = this.advertisements.filter(ad => {
      const shownCount = this.adShownCounts.get(ad.id) || 0;
      return shownCount < ad.freq;
    });
    
    if (availableAds.length === 0) {
      // All ads have reached their frequency limit
      this.clearAdTimers();
      console.log('All ads have reached their frequency limit');
      return;
    }
    
    // Select a random ad from available ones
    const randomIndex = Math.floor(Math.random() * availableAds.length);
    const selectedAd = availableAds[randomIndex];
    
    // Increment the shown count for this ad
    const currentCount = this.adShownCounts.get(selectedAd.id) || 0;
    this.adShownCounts.set(selectedAd.id, currentCount + 1);
    
    console.log(`Showing ad ${selectedAd.id}: ${currentCount + 1}/${selectedAd.freq} times`);

    // Set flag to wait for modal close before scheduling next ad
    this.isWaitingForModalClose = true;

    // Emit the ad to be displayed
    this.showAdSubject.next(selectedAd);
    
    // Check if we should stop after this ad
    if (this.adShownCounts.get(selectedAd.id) === selectedAd.freq) {
      console.log(`Ad ${selectedAd.id} has reached its frequency limit of ${selectedAd.freq}`);
    }
    
    // If all ads have reached their limit, stop the timers
    const allAdsReachedLimit = this.advertisements.every(ad => 
      (this.adShownCounts.get(ad.id) || 0) >= ad.freq
    );
    
    if (allAdsReachedLimit) {
      console.log('All ads have reached their frequency limits, stopping ad display');
      this.clearAdTimers();
    }
  }

  /**
   * Stop the ad display system
   */
  public stopAdDisplay(): void {
    this.clearAdTimers();
  }

  /**
   * Restart the ad display system
   */
  public restartAdDisplay(): void {
    this.clearAdTimers();
    this.loadAdvertisements();
  }

  /**
   * Clear all ad timers
   */
  private clearAdTimers(): void {
    if (this.adTimerId) {
      clearTimeout(this.adTimerId);
      this.adTimerId = null;
    }
    if (this.adIntervalId) {
      clearInterval(this.adIntervalId);
      this.adIntervalId = null;
    }
  }

  /**
   * Get current advertisements
   */
  public getAdvertisements(): Advertisement[] {
    return this.advertisements;
  }

  /**
   * Get ad shown counts
   */
  public getAdShownCounts(): Map<number, number> {
    return new Map(this.adShownCounts);
  }

  /**
   * Notify that the modal has been closed
   */
  public notifyModalClosed(): void {
    this.modalClosedSubject.next();
  }

  /**
   * Cleanup when service is destroyed
   */
  public ngOnDestroy(): void {
    this.clearAdTimers();
    this.destroy$.next();
    this.destroy$.complete();
  }
}
